"use client";

import { useState } from "react";
import { Button } from "@workspace/ui/components/button";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@workspace/ui/components/dialog";

export default function Page() {
  const [open1, setOpen1] = useState(false);
  const [open2, setOpen2] = useState(false);
  const [open3, setOpen3] = useState(false);
  const [open4, setOpen4] = useState(false);

  return (
    <>
      <div className="p-6">
        <Button variant="primary" onClick={() => setOpen1(true)}>
          {"1. Seviyeyi Aç"}
        </Button>
      </div>
      <Dialog open={open1} onOpenChange={setOpen1}>
        <DialogContent className="sm:max-w-[1000px] h-[700px]">
          <DialogHeader>
            <DialogTitle>{"1. <PERSON>vi<PERSON> Diyalog"}</DialogTitle>
            <DialogDescription>
              {
                "Bu sayfa iç içe geçmiş diyalogları (nested dialogs) gösterir. Toplam 4 seviye vardır."
              }
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="primary" onClick={() => setOpen2(true)}>
              {"2. Seviyeyi Aç"}
            </Button>
          </DialogFooter>
        </DialogContent>

        {/* Level 2 */}
        <Dialog open={open2} onOpenChange={setOpen2}>
          <DialogContent className="sm:max-w-[900px] h-[600px]">
            <DialogHeader>
              <DialogTitle>{"2. Seviye Diyalog"}</DialogTitle>
              <DialogDescription>
                {
                  "Üstteki diyaloğun içinde açıldı. Bir seviye daha açabilirsiniz."
                }
              </DialogDescription>
            </DialogHeader>
            <DialogFooter>
              <DialogClose asChild>
                <Button variant="secondary">{"2. Seviyeyi Kapat"}</Button>
              </DialogClose>
              <Button variant="primary" onClick={() => setOpen3(true)}>
                {"3. Seviyeyi Aç"}
              </Button>
            </DialogFooter>
          </DialogContent>
          {/* Level 3 */}
          <Dialog open={open3} onOpenChange={setOpen3}>
            <DialogContent className="sm:max-w-[800px] h-[500px]">
              <DialogHeader>
                <DialogTitle>{"3. Seviye Diyalog"}</DialogTitle>
                <DialogDescription>
                  {
                    "Bir seviye daha içe gidebilirsiniz. Sonraki seviye son seviyedir."
                  }
                </DialogDescription>
              </DialogHeader>
              <DialogFooter>
                <DialogClose asChild>
                  <Button variant="secondary">{"3. Seviyeyi Kapat"}</Button>
                </DialogClose>
                <Button variant="primary" onClick={() => setOpen4(true)}>
                  {"4. Seviyeyi Aç"}
                </Button>
              </DialogFooter>
            </DialogContent>
            {/* Level 4 */}
            <Dialog open={open4} onOpenChange={setOpen4}>
              <DialogContent className="sm:max-w-[700px] h-[400px]">
                <DialogHeader>
                  <DialogTitle>{"4. Seviye Diyalog"}</DialogTitle>
                  <DialogDescription>
                    {
                      "Bu son seviye. Kapatınca bir önceki seviyeye geri dönersiniz."
                    }
                  </DialogDescription>
                </DialogHeader>
                <DialogFooter className="gap-2">
                  <DialogClose asChild>
                    <Button variant="secondary">{"4. Seviyeyi Kapat"}</Button>
                  </DialogClose>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </Dialog>
        </Dialog>
      </Dialog>
    </>
  );
}
