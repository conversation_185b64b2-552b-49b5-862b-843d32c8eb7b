"use client";

import * as React from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from "@workspace/ui/components/dialog";

import { SectionLabel } from "@workspace/ui/components/section-label";
import { Separator } from "@workspace/ui/components/separator";
import { CopyButton } from "@workspace/ui/components/copy-button";
import { AlertShape } from "@workspace/ui/components/shapes/alert-shape";
import { InfoDialog } from "./info.dialog";
import Image from "next/image";

interface Bank {
  name: string;
  image: string;
  accountName: string;
  iban: string;
  description?: string;
}

interface BankDepositDialogProps {
  banks: Bank[];
  descriptionCode: string;
}

export function BankDepositDialog({
  banks,
  descriptionCode,
}: BankDepositDialogProps) {
  const [selectedBank, setSelectedBank] = React.useState<Bank | null>(() =>
    banks.length > 0 ? banks[0]! : null,
  );

  const handleBankSelect = (
    bank: Bank,
    event: React.MouseEvent<HTMLButtonElement>,
  ) => {
    setSelectedBank(bank);
    event.currentTarget.scrollIntoView({
      behavior: "smooth",
      inline: "center",
      block: "center",
    });
  };

  return (
    <Dialog open>
      <form>
        <DialogContent className="sm:max-w-[640px]">
          <DialogHeader>
            <DialogTitle>{"Havale ile Para Yatır"}</DialogTitle>
          </DialogHeader>
          <section
            id="deposit-info"
            className="grid grid-cols-1 sm:grid-cols-8 gap-5 mx-2 sm:mx-4 sm:max-h-86"
          >
            <nav
              id="bank-list"
              className="sm:col-span-3 overflow-auto scrollbar"
            >
              <div className="flex flex-row sm:flex-col my-2 sm:m-0 gap-3 pr-4 p-2  after:content-[''] after:pr-2 sm:after:content-none">
                {banks.map((bank) => (
                  <button
                    key={bank.name}
                    onClick={(event) => handleBankSelect(bank, event)}
                    className="border-3 rounded-sm overflow-hidden border-background ring-foreground ring-2 w-40 sm:w-full h-20 relative flex-shrink-0"
                  >
                    <Image
                      src={bank.image}
                      alt={bank.name}
                      fill
                      className="object-cover"
                    />
                    {selectedBank?.name === bank.name && (
                      <div
                        id="selected-badge"
                        className="bg-primary absolute px-3 skew-x-24 text-xs top-0 -right-1 rounded-xs"
                      >
                        <div className="-skew-x-24">{"seçildi"}</div>
                      </div>
                    )}
                  </button>
                ))}
              </div>
            </nav>
            <article
              id="bank-info"
              className="mt-1.5 p-4 sm:col-span-5 bg-muted rounded-lg flex flex-col h-full overflow-auto scrollbar"
            >
              <div className="flex justify-between items-start">
                <SectionLabel>{"HESAP ADI"}</SectionLabel>
                <CopyButton
                  text={selectedBank?.accountName || ""}
                  aria-label="Hesap adını kopyala"
                />
              </div>
              <p className="text-foreground text-sm px-1 mb-5">
                {selectedBank?.accountName || "Hesap adı bulunamadı"}
              </p>
              <Separator />
              <div className="flex justify-between items-start">
                <SectionLabel>{"IBAN NO"}</SectionLabel>
                <CopyButton
                  text={selectedBank?.iban || ""}
                  aria-label="IBAN numarasını kopyala"
                />
              </div>
              <p className="text-foreground text-sm px-1 mb-5">
                {selectedBank?.iban || "IBAN bulunamadı"}
              </p>
              <Separator />
              <div className="flex justify-between items-start">
                <SectionLabel>{"AÇIKLAMA"}</SectionLabel>
                <CopyButton
                  text={descriptionCode}
                  aria-label="Açıklama kodunu kopyala"
                />
              </div>
              <p className="text-foreground text-sm px-1 mb-5">
                {descriptionCode}
              </p>
            </article>
          </section>
          <Separator />
          <DialogFooter>
            <div className="flex gap-4 items-center mx-4 -mt-5 mb-4">
              <AlertShape>!</AlertShape>
              <p className="text-muted-foreground text-xs leading-5">
                {"Lütfen aktarım yapmadan önce "}
                <InfoDialog
                  trigger={
                    <button className="underline underline-offset-4 decoration-2 hover:text-foreground text-accent-foreground">
                      {"bilgilendirme metnini"}
                    </button>
                  }
                  title="Bilgilendirme"
                  items={[
                    "Sadece kendi adınıza ait hesaptan havale yapabilirsiniz.",
                    "Havale işlemi gerçekleştirirken açıklama kısmına mutlaka verilen kodu yazınız.",
                    "Para yatırma işlemi maksimum 3 iş günü içerisinde hesabınıza yansıyacaktır.",
                    "Yatırılan tutarın her 10 TL'lik kısmı 1 Soda'ya tekabül ederken, bu dönüşümden arta kalan bakiye Kapak birimine çevrilir.",
                    "Geciken havale işlemleri için destek ekibimizle iletişime geçin.",
                    "Para iadesi talep edilmesi durumunda EFT ücreti kesilerek iade yapılır. EFT ücretinden düşük tutarlı ödemeler iade edilmez.",
                  ]}
                />
                {" okuyun."}
              </p>
            </div>
          </DialogFooter>
        </DialogContent>
      </form>
    </Dialog>
  );
}
