"use client";

import * as React from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  <PERSON><PERSON><PERSON>ooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@workspace/ui/components/dialog";
import { useDialogStore } from "@workspace/ui/stores/dialog-store";

export function NestedDialogsExample() {
  const [firstDialogOpen, setFirstDialogOpen] = React.useState(false);
  const [secondDialogOpen, setSecondDialogOpen] = React.useState(false);
  const [thirdDialogOpen, setThirdDialogOpen] = React.useState(false);
  
  // You can access the global dialog state from anywhere
  const { openDialogs } = useDialogStore();

  return (
    <div className="p-8">
      <h2 className="text-2xl font-bold mb-4">Nested Dialogs with Global State</h2>
      
      {/* Display current dialog state */}
      <div className="mb-4 p-4 bg-gray-100 rounded">
        <h3 className="font-semibold">Current Dialog State:</h3>
        <p>Open dialogs count: {openDialogs.size}</p>
        <ul>
          {Array.from(openDialogs.entries()).map(([level, id]) => (
            <li key={level}>Level {level}: {id.slice(-8)}</li>
          ))}
        </ul>
      </div>

      {/* First Dialog */}
      <Dialog open={firstDialogOpen} onOpenChange={setFirstDialogOpen}>
        <DialogTrigger asChild>
          <button className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">
            Open First Dialog
          </button>
        </DialogTrigger>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>First Dialog</DialogTitle>
            <DialogDescription>
              This is the first dialog. Notice how the overlay is visible.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <button
              onClick={() => setSecondDialogOpen(true)}
              className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
            >
              Open Second Dialog
            </button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Second Dialog */}
      <Dialog open={secondDialogOpen} onOpenChange={setSecondDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Second Dialog</DialogTitle>
            <DialogDescription>
              This is the second dialog. The first dialog should be hidden (opacity-0) 
              but the overlay remains from the first dialog.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <button
              onClick={() => setThirdDialogOpen(true)}
              className="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600"
            >
              Open Third Dialog
            </button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Third Dialog */}
      <Dialog open={thirdDialogOpen} onOpenChange={setThirdDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Third Dialog</DialogTitle>
            <DialogDescription>
              This is the third dialog. Only this dialog should be visible, 
              while the previous two are hidden.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <button
              onClick={() => setThirdDialogOpen(false)}
              className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600"
            >
              Close This Dialog
            </button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}

// Example of accessing dialog state from anywhere in your app
export function DialogStateMonitor() {
  const { openDialogs, getIsLastDialog, getIsFirstDialog } = useDialogStore();
  
  return (
    <div className="fixed top-4 right-4 p-4 bg-white border rounded shadow-lg">
      <h4 className="font-semibold mb-2">Dialog Monitor</h4>
      <p>Active dialogs: {openDialogs.size}</p>
      {Array.from(openDialogs.entries()).map(([level, id]) => (
        <div key={level} className="text-sm">
          Level {level}: {id.slice(-8)} 
          {getIsFirstDialog(id) && " (First)"}
          {getIsLastDialog(id) && " (Last)"}
        </div>
      ))}
    </div>
  );
}
